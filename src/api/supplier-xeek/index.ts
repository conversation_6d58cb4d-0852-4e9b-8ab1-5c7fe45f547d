import request from "@/utils/request";
import { type SnCodeItem } from "./types";

const Supplier_Xeek_Info = "/supplier/xeek/Info";
const Supplier_Xeek_Api = {
  getSendSnCodeRecord(params: {
    pageIndex: number;
    pageSize: number;
    keywords: string;
  }): Promise<ServerResult<ListDataTotal<SnCodeItem>>> {
    return request.get(`${Supplier_Xeek_Info}/GetSendSnCodeRecord`, { params });
  },
};

export default Supplier_Xeek_Api;
