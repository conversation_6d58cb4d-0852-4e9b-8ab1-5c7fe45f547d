<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="sn码"
                  class="120px!"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleAddClick">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column label="设备SN码" align="center">
            <template #default="scope">
              {{ handleGetSnCode(scope.row) }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import Supplier_Xeek_Api from "@/api/supplier-xeek";
import { SnCodeItem } from "@/api/supplier-xeek/types";
import { useTableConfig } from "@/hooks/useTableConfig";

defineOptions({
  name: "SekeDeviceManage",
});

const queryParams = ref<any>({
  pageIndex: 1,
  pageSize: 20,
  keywords: "",
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<SnCodeItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetSnCode = (row: SnCodeItem) => {
  return row.RequestData?.sn_list || "";
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Xeek_Api.getSendSnCodeRecord(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};
const handleAddClick = () => {};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
