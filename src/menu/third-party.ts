import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/third-party",
    component: "Layout",
    name: "third-party",
    redirect: "/third-party/DeviceBinding",
    meta: {
      title: "第三方设备",
      hidden: false,
      roles: ["assistant", "finance", "operations", "storage"],
    },
    children: [
      {
        path: "DeviceBinding",
        name: "<PERSON><PERSON>Binding",
        component: "third-party/DeviceBinding",
        meta: {
          title: "设备绑定",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "SekeDeviceManage",
        name: "SekeDeviceManage",
        component: "third-party/SekeDeviceManage/index",
        meta: {
          title: "赛客设备管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default routes;
